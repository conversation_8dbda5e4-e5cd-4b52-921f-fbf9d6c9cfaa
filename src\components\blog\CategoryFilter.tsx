'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Filter, X, Tag, Hash } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

interface Category {
  _id: string;
  name: string;
  slug: string;
  count: number;
}

interface CategoryFilterProps {
  onCategoryChange?: (categoryId: string | null) => void;
  onSearchChange?: (search: string) => void;
  className?: string;
}

export function CategoryFilter({ 
  onCategoryChange, 
  onSearchChange, 
  className = '' 
}: CategoryFilterProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showAllCategories, setShowAllCategories] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();

  // Load categories on component mount
  useEffect(() => {
    fetchCategories();
    
    // Get initial values from URL params
    const categoryParam = searchParams.get('category');
    const searchParam = searchParams.get('search');
    
    if (categoryParam) {
      setSelectedCategory(categoryParam);
    }
    if (searchParam) {
      setSearchTerm(searchParam);
    }
  }, [searchParams]);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/categories');
      
      if (response.ok) {
        const data = await response.json();
        const categoriesData = data.success ? data.data : data;
        
        // Sort categories by post count (descending) and then by name
        const sortedCategories = categoriesData.sort((a: Category, b: Category) => {
          if (b.count !== a.count) {
            return b.count - a.count;
          }
          return a.name.localeCompare(b.name);
        });
        
        setCategories(sortedCategories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategorySelect = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
    
    // Update URL params
    const params = new URLSearchParams(searchParams.toString());
    if (categoryId) {
      params.set('category', categoryId);
    } else {
      params.delete('category');
    }
    
    router.push(`?${params.toString()}`, { scroll: false });
    
    // Call parent callback
    if (onCategoryChange) {
      onCategoryChange(categoryId);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    
    // Update URL params
    const params = new URLSearchParams(searchParams.toString());
    if (value.trim()) {
      params.set('search', value);
    } else {
      params.delete('search');
    }
    
    router.push(`?${params.toString()}`, { scroll: false });
    
    // Call parent callback
    if (onSearchChange) {
      onSearchChange(value);
    }
  };

  const clearFilters = () => {
    setSelectedCategory(null);
    setSearchTerm('');
    
    // Clear URL params
    router.push(window.location.pathname, { scroll: false });
    
    // Call parent callbacks
    if (onCategoryChange) {
      onCategoryChange(null);
    }
    if (onSearchChange) {
      onSearchChange('');
    }
  };

  const displayCategories = showAllCategories ? categories : categories.slice(0, 8);
  const hasMoreCategories = categories.length > 8;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`bg-card border rounded-lg p-6 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Filter & Search</h3>
        </div>
        
        {(selectedCategory || searchTerm) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </Button>
        )}
      </div>

      {/* Search Input */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search articles..."
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Categories */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Tag className="h-4 w-4 text-primary" />
          <h4 className="font-medium">Categories</h4>
        </div>

        {loading ? (
          <div className="flex flex-wrap gap-2">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="h-8 w-20 bg-muted animate-pulse rounded-full"
              />
            ))}
          </div>
        ) : (
          <>
            {/* All Categories Button */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Badge
                variant={selectedCategory === null ? "default" : "secondary"}
                className={`
                  cursor-pointer transition-all duration-200 px-4 py-2 text-sm
                  ${selectedCategory === null 
                    ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
                    : 'hover:bg-secondary/80'
                  }
                `}
                onClick={() => handleCategorySelect(null)}
              >
                <Hash className="h-3 w-3 mr-1" />
                All Categories
                <span className="ml-2 text-xs opacity-75">
                  ({categories.reduce((sum, cat) => sum + cat.count, 0)})
                </span>
              </Badge>
            </motion.div>

            {/* Category List */}
            <div className="flex flex-wrap gap-2">
              {displayCategories.map((category) => (
                <motion.div
                  key={category._id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Badge
                    variant={selectedCategory === category._id ? "default" : "secondary"}
                    className={`
                      cursor-pointer transition-all duration-200 px-3 py-2 text-sm
                      ${selectedCategory === category._id 
                        ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
                        : 'hover:bg-secondary/80'
                      }
                    `}
                    onClick={() => handleCategorySelect(category._id)}
                  >
                    {category.name}
                    <span className="ml-2 text-xs opacity-75">
                      ({category.count})
                    </span>
                  </Badge>
                </motion.div>
              ))}
            </div>

            {/* Show More/Less Button */}
            {hasMoreCategories && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAllCategories(!showAllCategories)}
                className="text-primary hover:text-primary/80"
              >
                {showAllCategories ? 'Show Less' : `Show ${categories.length - 8} More`}
              </Button>
            )}
          </>
        )}
      </div>

      {/* Active Filters Summary */}
      {(selectedCategory || searchTerm) && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-6 pt-4 border-t"
        >
          <p className="text-sm text-muted-foreground mb-2">Active filters:</p>
          <div className="flex flex-wrap gap-2">
            {selectedCategory && (
              <Badge variant="outline" className="text-xs">
                Category: {categories.find(c => c._id === selectedCategory)?.name}
              </Badge>
            )}
            {searchTerm && (
              <Badge variant="outline" className="text-xs">
                Search: "{searchTerm}"
              </Badge>
            )}
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}
