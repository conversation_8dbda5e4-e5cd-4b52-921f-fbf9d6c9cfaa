import { NextRequest, NextResponse } from "next/server";
import { extractFeaturedImageFromContent } from "@/lib/imageUtils";

export async function POST(request: NextRequest) {
  try {
    const { content } = await request.json();
    
    if (!content) {
      return NextResponse.json(
        { error: "Content is required" },
        { status: 400 }
      );
    }

    const featuredImage = extractFeaturedImageFromContent(content);
    
    return NextResponse.json({
      success: true,
      content,
      featuredImage,
      hasImage: !!featuredImage
    });
  } catch (error) {
    console.error("Error testing featured image extraction:", error);
    return NextResponse.json(
      { error: "Failed to test featured image extraction" },
      { status: 500 }
    );
  }
}

// Test endpoint to verify featured image extraction
export async function GET() {
  const testCases = [
    {
      name: "HTML with image",
      content: '<p>This is a test post.</p><img src="/uploads/images/test.jpg" alt="Test image" /><p>More content here.</p>'
    },
    {
      name: "HTML with multiple images",
      content: '<p>First paragraph.</p><img src="/uploads/images/first.jpg" alt="First" /><p>Middle content.</p><img src="/uploads/images/second.jpg" alt="Second" /><p>End.</p>'
    },
    {
      name: "HTML without images",
      content: '<p>This is a test post without any images.</p><p>Just text content here.</p>'
    },
    {
      name: "HTML with external image",
      content: '<p>Test with external image.</p><img src="https://example.com/image.jpg" alt="External" />'
    }
  ];

  const results = testCases.map(testCase => ({
    ...testCase,
    featuredImage: extractFeaturedImageFromContent(testCase.content),
    hasImage: !!extractFeaturedImageFromContent(testCase.content)
  }));

  return NextResponse.json({
    success: true,
    testResults: results
  });
}
